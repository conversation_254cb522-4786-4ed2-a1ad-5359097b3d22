"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for PhieuXuatTraLaiNhaCungCap (Supplier Return Note) model.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401,

# Import các model cần thiết
from django_ledger.models import (  # noqa: F401,
    AccountModel,
    CustomerModel,
    EntityModel,
    EntityUnitModel,
    HinhThucThanhToanModel,
    NgoaiTeModel,
    NhanVienModel,
    NhapXuatModel,
    TaxModel,
)
from django_ledger.models.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_xuat_tra_lai_nha_cung_cap import (  # noqa: F401,
    PhieuXuatTraLaiNhaCungCapModel,
)
from django_ledger.models.phuong_thuc_thanh_toan import (
    PhuongThucThanhToanModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class PhieuXuatTraLaiNhaCungCapRepository(BaseRepository):
    """
    Repository for handling PhieuXuatTraLaiNhaCungCap model database operations
    """

    def __init__(self):  # noqa: C901
        self.model = PhieuXuatTraLaiNhaCungCapModel

    def convert_uuids_to_model_instances(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Convert UUID strings to model instances for foreign key fields

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances
        """
        # Make a copy of the data to avoid modifying the original
        data_copy = data.copy()
        # ma_ngv is CharField, no conversion needed

        # Convert ma_kh UUID to CustomerModel instance
        if 'ma_kh' in data_copy and data_copy['ma_kh']:
            try:
                data_copy['ma_kh'] = CustomerModel.objects.get(
                    uuid=data_copy['ma_kh']
                )
            except (CustomerModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_nx UUID to NhapXuatModel instance
        if 'ma_nx' in data_copy and data_copy['ma_nx']:
            try:
                data_copy['ma_nx'] = NhapXuatModel.objects.get(
                    uuid=data_copy['ma_nx']
                )
            except (NhapXuatModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_tt UUID to HinhThucThanhToanModel instance
        if 'ma_tt' in data_copy and data_copy['ma_tt']:
            try:
                data_copy['ma_tt'] = HinhThucThanhToanModel.objects.get(
                    uuid=data_copy['ma_tt']
                )
            except (HinhThucThanhToanModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert tk UUID to AccountModel instance
        if 'tk' in data_copy and data_copy['tk']:
            try:
                data_copy['tk'] = AccountModel.objects.get(uuid=data_copy['tk'])
            except (AccountModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert unit_id UUID to EntityUnitModel instance
        if 'unit_id' in data_copy and data_copy['unit_id']:
            try:
                data_copy['unit_id'] = EntityUnitModel.objects.get(
                    uuid=data_copy['unit_id']
                )
            except (EntityUnitModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_nt UUID to NgoaiTeModel instance
        if 'ma_nt' in data_copy and data_copy['ma_nt']:
            try:
                data_copy['ma_nt'] = NgoaiTeModel.objects.get(
                    uuid=data_copy['ma_nt']
                )
            except (NgoaiTeModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_thue UUID to TaxModel instance
        if 'ma_thue' in data_copy and data_copy['ma_thue']:
            try:
                data_copy['ma_thue'] = TaxModel.objects.get(
                    uuid=data_copy['ma_thue']
                )
            except (TaxModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert tk_thue UUID to AccountModel instance
        if 'tk_thue' in data_copy and data_copy['tk_thue']:
            try:
                data_copy['tk_thue'] = AccountModel.objects.get(
                    uuid=data_copy['tk_thue']
                )
            except (AccountModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_kh9 UUID to CustomerModel instance
        if 'ma_kh9' in data_copy and data_copy['ma_kh9']:
            try:
                data_copy['ma_kh9'] = CustomerModel.objects.get(
                    uuid=data_copy['ma_kh9']
                )
            except (CustomerModel.DoesNotExist, ValueError, TypeError):
                pass

        # Convert ma_pttt UUID to PhuongThucThanhToanModel instance
        if 'ma_pttt' in data_copy and data_copy['ma_pttt']:
            try:
                data_copy['ma_pttt'] = PhuongThucThanhToanModel.objects.get(
                    uuid=data_copy['ma_pttt']
                )
            except (PhuongThucThanhToanModel.DoesNotExist, ValueError, TypeError):
                pass

        return data_copy

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Get the base queryset for the model

        Returns
        -------
        QuerySet
            Base queryset for the model
        """
        return self.model.objects.all()

    def get_for_entity(
        self, entity_slug: str, user_model
    ) -> QuerySet:  # noqa: C901
        """
        Get supplier return notes for a specific entity

        Parameters
        ----------
        entity_slug : str
            The entity slug
        user_model : UserModel
            The user model

        Returns
        -------
        QuerySet
            QuerySet of PhieuXuatTraLaiNhaCungCapModel instances
        """
        return self.model.objects.for_entity(
            entity_slug=entity_slug, user_model=user_model
        )

    def get_by_uuid(
        self, uuid: UUID
    ) -> Optional[PhieuXuatTraLaiNhaCungCapModel]:  # noqa: C901
        """
        Get a supplier return note by UUID

        Parameters
        ----------
        uuid : UUID
            The UUID of the supplier return note

        Returns
        -------
        Optional[PhieuXuatTraLaiNhaCungCapModel]
            The supplier return note if found, None otherwise
        """
        try:
            return self.model.objects.get(uuid=uuid)
        except self.model.DoesNotExist:
            return None

    def create(
        self, entity_slug: str, user_model, data: Dict[str, Any]
    ) -> PhieuXuatTraLaiNhaCungCapModel:  # noqa: C901
        """
        Create a new supplier return note

        Parameters
        ----------
        entity_slug : str
            The entity slug
        user_model : UserModel
            The user model
        data : Dict[str, Any]
            The data to create the supplier return note with

        Returns
        -------
        PhieuXuatTraLaiNhaCungCapModel
            The created supplier return note
        """
        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)
        # Get entity model
        entity_model = get_object_or_404(EntityModel, slug=entity_slug)
        # Add entity model to data
        data['entity_model'] = entity_model
        # Create supplier return note
        return self.model.objects.create(**data)

    def update(
        self, uuid: UUID, data: Dict[str, Any]
    ) -> Optional[PhieuXuatTraLaiNhaCungCapModel]:  # noqa: C901
        """
        Update an existing supplier return note

        Parameters
        ----------
        uuid : UUID
            The UUID of the supplier return note to update
        data : Dict[str, Any]
            The data to update the supplier return note with

        Returns
        -------
        Optional[PhieuXuatTraLaiNhaCungCapModel]
            The updated supplier return note if found, None otherwise
        """
        instance = self.get_by_uuid(uuid)
        if instance:
            # Convert UUIDs to model instances
            data = self.convert_uuids_to_model_instances(data)
            for key, value in data.items():
                setattr(instance, key, value)
            instance.save()
            return instance
        return None

    def delete(self, uuid: UUID) -> bool:  # noqa: C901
        """
        Delete a supplier return note

        Parameters
        ----------
        uuid : UUID
            The UUID of the supplier return note to delete

        Returns
        -------
        bool
            True if the supplier return note was deleted, False otherwise
        """
        instance = self.get_by_uuid(uuid)
        if instance:
            instance.delete()
            return True
        return False
