"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Views for PhieuChiChiTiet model.
"""

from drf_spectacular.utils import (  # noqa: F401
    extend_schema,
    extend_schema_view,
)
from rest_framework import permissions, status, viewsets  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.decorators import api_exception_handler  # noqa: F401
from django_ledger.api.serializers.tien_mat.hach_toan.phieu_chi.phieu_chi_chi_tiet import (  # noqa: F401,
    PhieuChiChiTietSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models import PhieuChiChiTietModel  # noqa: F401,
from django_ledger.services.tien_mat.hach_toan.phieu_chi.phieu_chi_chi_tiet import (  # noqa: F401,
    PhieuChiChiTietService,
)


@extend_schema_view(
    list=extend_schema(
        description="List all payment voucher details for a payment voucher"
    ),
    create=extend_schema(description="Create a new payment voucher detail"),
    retrieve=extend_schema(
        description="Retrieve a specific payment voucher detail"
    ),
    update=extend_schema(description="Update a payment voucher detail"),
    partial_update=extend_schema(
        description="Partially update a payment voucher detail"
    ),
    destroy=extend_schema(description="Delete a payment voucher detail"),
)
class PhieuChiChiTietViewSet(EntityRelatedViewSet):
    """
    A ViewSet for PhieuChiChiTiet model.
    """

    serializer_class = PhieuChiChiTietSerializer  # noqa: F811
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'uuid'
    http_method_names = ['get', 'post', 'put', 'patch', 'delete']

    def __init__(self, *args, **kwargs):  # noqa: C901
        """
        Initialize the ViewSet.

        Parameters
        ----------
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.service = PhieuChiChiTietService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for the ViewSet.

        Returns
        -------
        QuerySet
            The queryset for the ViewSet
        """
        phieu_chi_uuid = self.kwargs.get('phieu_chi_uuid')
        if phieu_chi_uuid:
            return self.service.get_by_phieu_chi(phieu_chi_uuid)
        return PhieuChiChiTietModel.objects.none()

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new PhieuChiChiTiet instance.

        Parameters
        ----------
        request : Request
            The request object
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The response object
        """
        from django_ledger.services.tien_mat.hach_toan.phieu_chi.phieu_chi import (  # noqa: F401,
            PhieuChiService,
        )

        phieu_chi_uuid = kwargs.get('phieu_chi_uuid')
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # Get the parent PhieuChi instance
        phieu_chi_service = PhieuChiService()
        parent_instance = phieu_chi_service.get_by_uuid(phieu_chi_uuid)
        instance = self.service.create(
            parent_field=parent_instance, data=serializer.validated_data
        )

        return Response(
            self.get_serializer(instance).data,
            status=status.HTTP_201_CREATED,
        )

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update an existing PhieuChiChiTiet instance.

        Parameters
        ----------
        request : Request
            The request object
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The response object
        """
        uuid = kwargs.get('uuid')
        partial = kwargs.pop('partial', False)
        serializer = self.get_serializer(data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        instance = self.service.update(
            uuid=uuid, data=serializer.validated_data
        )
        return Response(
            self.get_serializer(instance).data, status=status.HTTP_200_OK
        )

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a PhieuChiChiTiet instance.

        Parameters
        ----------
        request : Request
            The request object
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The response object
        """
        uuid = kwargs.get('uuid')
        self.service.delete(uuid=uuid)
        return Response(status=status.HTTP_204_NO_CONTENT)

    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a specific PhieuChiChiTiet instance.

        Parameters
        ----------
        request : Request
            The request object
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The response object
        """
        uuid = kwargs.get('uuid')
        instance = self.service.get_by_uuid(uuid)
        return Response(
            self.get_serializer(instance).data, status=status.HTTP_200_OK
        )
