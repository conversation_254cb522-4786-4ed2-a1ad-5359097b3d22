"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

View for HoaDonDieuChinhGiaHangBan (Price Adjustment Invoice) model.
"""

from rest_framework import status, viewsets  # noqa: F401
from rest_framework.decorators import action  # noqa: F401
from rest_framework.permissions import IsAuthenticated  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.decorators import api_exception_handler  # noqa: F401
from django_ledger.api.serializers.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dieu_chinh_gia_hang_ban import (  # noqa: F401,
    ChiTietHoaDonDieuChinhGiaHangBanModelSerializer,
    HoaDonDieuChinhGiaHangBanModelCreateUpdateSerializer,
    HoaDonDieuChinhGiaHangBanModelSerializer,
)
from django_ledger.services.ban_hang.hoa_don_dieu_chinh_tra_lai.hoa_don_dieu_chinh_gia_hang_ban import (  # noqa: F401,
    ChiTietHoaDonDieuChinhGiaHangBanService,
    HoaDonDieuChinhGiaHangBanService,
)


class HoaDonDieuChinhGiaHangBanViewSet(viewsets.ModelViewSet):
    """
    ViewSet for the HoaDonDieuChinhGiaHangBan model.
    """

    permission_classes = [IsAuthenticated]
    lookup_field = "uuid"
    http_method_names = [
        "get",
        "post",
        "put",
        "patch",
        "delete",
        "head",
        "options",
    ]

    def __init__(self, *args, **kwargs):  # noqa: C901
        super().__init__(*args, **kwargs)
        self.service = HoaDonDieuChinhGiaHangBanService()
        self.chi_tiet_service = ChiTietHoaDonDieuChinhGiaHangBanService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for the view.

        Returns
        -------
        QuerySet
            The queryset for the view.
        """
        entity_slug = self.kwargs.get("entity_slug")
        return self.service.get_all(entity_slug)

    def get_serializer_class(self):  # noqa: C901
        """
        Get the serializer class for the view.

        Returns
        -------
        Serializer
            The serializer class for the view.
        """
        if self.action in ["create", "update", "partial_update"]:
            return HoaDonDieuChinhGiaHangBanModelCreateUpdateSerializer
        return HoaDonDieuChinhGiaHangBanModelSerializer

    @api_exception_handler
    def perform_create(self, serializer):  # noqa: C901
        """
        Perform the create operation.

        Parameters
        ----------
        serializer : Serializer
            The serializer instance.
        """
        entity_slug = self.kwargs.get("entity_slug")
        validated_data = serializer.validated_data
        validated_data["entity_model"] = entity_slug
        chi_tiet_data = validated_data.pop("chi_tiet", None)
        instance = self.service.create(validated_data, chi_tiet_data)
        serializer.instance = instance

    @api_exception_handler
    def perform_update(self, serializer):  # noqa: C901
        """
        Perform the update operation.

        Parameters
        ----------
        serializer : Serializer
            The serializer instance.
        """
        instance = self.get_object()
        validated_data = serializer.validated_data
        chi_tiet_data = validated_data.pop("chi_tiet", None)
        instance = self.service.update(instance.uuid, validated_data)
        # If chi_tiet_data is provided, handle it separately
        if chi_tiet_data:
            # Add the hoa_don reference to each chi_tiet
            for chi_tiet in chi_tiet_data:
                chi_tiet["hoa_don"] = instance.uuid
                # If uuid is provided, update the existing chi_tiet
                if "uuid" in chi_tiet:
                    uuid = chi_tiet.pop("uuid")
                    self.chi_tiet_service.update(uuid, chi_tiet)
                else:
                    # Otherwise, create a new chi_tiet
                    self.chi_tiet_service.create(chi_tiet)

        serializer.instance = instance

    @api_exception_handler
    def perform_destroy(self, instance):  # noqa: C901
        """
        Perform the destroy operation.

        Parameters
        ----------
        instance : HoaDonDieuChinhGiaHangBanModel
            The instance to destroy.
        """
        self.service.delete(instance.uuid)

    @action(detail=True, methods=["get"])
    def chi_tiet(self, request, entity_slug=None, uuid=None):  # noqa: F811,
        """
        Get the chi_tiet for a HoaDonDieuChinhGiaHangBan.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        uuid : str
            The UUID of the HoaDonDieuChinhGiaHangBan.

        Returns
        -------
        Response
            The response object.
        """
        chi_tiet_list = self.chi_tiet_service.get_by_invoice(uuid)
        serializer = ChiTietHoaDonDieuChinhGiaHangBanModelSerializer(
            chi_tiet_list, many=True
        )

        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=False, methods=["get"])
    def search(self, request, entity_slug=None):  # noqa: F811,
        """
        Search for HoaDonDieuChinhGiaHangBan instances.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.

        Returns
        -------
        Response
            The response object.
        """
        query = request.query_params.get("q", "")
        queryset = self.service.search(entity_slug, query)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
