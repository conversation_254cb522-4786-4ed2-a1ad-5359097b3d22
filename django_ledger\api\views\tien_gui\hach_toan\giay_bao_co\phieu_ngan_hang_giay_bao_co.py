"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Views for PhieuNganHangGiayBaoCo model.
"""

from drf_spectacular.utils import (  # noqa: F401
    extend_schema,
    extend_schema_view,
)
from rest_framework import permissions, status  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.decorators import api_exception_handler  # noqa: F401
from django_ledger.api.serializers.tien_gui.hach_toan.giay_bao_co.phieu_ngan_hang_giay_bao_co import (  # noqa: F401,
    PhieuNganHangGiayBaoCoSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models import PhieuNganHangGiayBaoCoModel  # noqa: F401,
from django_ledger.services.tien_gui.hach_toan.giay_bao_co.phieu_ngan_hang_giay_bao_co import (  # noqa: F401,
    PhieuNganHangGiayBaoCoService,
)


@extend_schema_view(
    list=extend_schema(tags=["PhieuNganHangGiayBaoCo"]),
    create=extend_schema(tags=["PhieuNganHangGiayBaoCo"]),
    retrieve=extend_schema(tags=["PhieuNganHangGiayBaoCo"]),
    update=extend_schema(tags=["PhieuNganHangGiayBaoCo"]),
    partial_update=extend_schema(tags=["PhieuNganHangGiayBaoCo"]),
    destroy=extend_schema(tags=["PhieuNganHangGiayBaoCo"]),
)
class PhieuNganHangGiayBaoCoViewSet(EntityRelatedViewSet):
    """
    A ViewSet for PhieuNganHangGiayBaoCo model.
    """

    queryset = PhieuNganHangGiayBaoCoModel.objects.all()
    serializer_class = PhieuNganHangGiayBaoCoSerializer  # noqa: F811
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'uuid'
    http_method_names = ['get', 'post', 'put', 'delete']

    def __init__(self, *args, **kwargs):  # noqa: C901
        """
        Initialize the ViewSet.

        Parameters
        ----------
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.service = PhieuNganHangGiayBaoCoService()

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List all PhieuNganHangGiayBaoCo instances for a specific GiayBaoCo.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get the parent UUID from the URL
        giay_bao_co_uuid = self.kwargs.get('giay_bao_co_uuid')
        # Get data from service
        instances = self.service.list(giay_bao_co_uuid=giay_bao_co_uuid)
        # Always paginate the queryset
        page = self.paginate_queryset(instances)
        serializer = self.get_serializer(page, many=True)
        return self.get_paginated_response(serializer.data)

    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a specific PhieuNganHangGiayBaoCo instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Get the instance
        instance = self.service.get(
            entity_slug=entity_slug, uuid=kwargs['uuid']
        )
        if not instance:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Serialize data
        serializer = self.get_serializer(instance)
        # Return response
        return Response(serializer.data)

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new PhieuNganHangGiayBaoCo instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Validate data
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # Get the parent UUID from the URL
        giay_bao_co_uuid = self.kwargs.get('giay_bao_co_uuid')
        try:
            # Create instance
            instance = self.service.create(
                entity_slug=entity_slug,
                giay_bao_co_uuid=giay_bao_co_uuid,
                data=serializer.validated_data,
            )
        except Exception as e:
            return Response(
                {'detail': 'Parent not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Serialize the created instance
        result_serializer = self.get_serializer(instance)
        # Return response
        return Response(result_serializer.data, status=status.HTTP_201_CREATED)

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update an existing PhieuNganHangGiayBaoCo instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Validate data
        serializer = self.get_serializer(
            data=request.data, partial=kwargs.get('partial', False)
        )
        serializer.is_valid(raise_exception=True)
        try:
            # Update instance
            instance = self.service.update(
                uuid=kwargs['uuid'], data=serializer.validated_data
            )
        except Exception as e:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        if not instance:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Serialize the updated instance
        result_serializer = self.get_serializer(instance)
        # Return response
        return Response(result_serializer.data)

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete an existing PhieuNganHangGiayBaoCo instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        try:
            # Delete instance
            self.service.delete(uuid=kwargs['uuid'])
        except Exception as e:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Return response
        return Response(status=status.HTTP_204_NO_CONTENT)
