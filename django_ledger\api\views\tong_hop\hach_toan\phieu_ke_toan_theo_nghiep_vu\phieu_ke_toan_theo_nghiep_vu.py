"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ViewSet for PhieuKeToaNghiepVu model.
"""

from rest_framework import permissions, status  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.decorators import api_exception_handler  # noqa: F401
from django_ledger.api.serializers.tong_hop.hach_toan.phieu_ke_toan_theo_nghiep_vu import (  # noqa: F401
    PhieuKeToaNghiepVuSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.services.tong_hop.hach_toan.phieu_ke_toan_theo_nghiep_vu import (  # noqa: F401,
    PhieuKeToaNghiepVuService,
)


class PhieuKeToaNghiepVuViewSet(EntityRelatedViewSet):
    """
    A ViewSet for PhieuKeToaNghiepVu model.
    """

    serializer_class = PhieuKeToaNghiepVuSerializer  # noqa: F811
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'uuid'
    http_method_names = ['get', 'post', 'put', 'delete']

    def __init__(self, *args, **kwargs):  # noqa: C901
        """
        Initialize the ViewSet.

        Parameters
        ----------
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.service = PhieuKeToaNghiepVuService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for PhieuKeToaNghiepVu.

        Returns
        -------
        QuerySet
            The filtered queryset
        """
        entity_slug = self.kwargs.get('entity_slug')
        return self.service.list(
            entity_slug=entity_slug, user_model=self.request.user
        )

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List PhieuKeToaNghiepVu instances.

        Parameters
        ----------
        request : Request
            The HTTP request
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The HTTP response with paginated data
        """
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        serializer = self.get_serializer(page, many=True)
        return self.get_paginated_response(serializer.data)

    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a PhieuKeToaNghiepVu instance.

        Parameters
        ----------
        request : Request
            The HTTP request
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The HTTP response with the instance data
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = self.kwargs.get('uuid')
        instance = self.service.get_with_details(
            entity_slug=entity_slug, uuid=uuid
        )
        if not instance:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new PhieuKeToaNghiepVu instance.

        Parameters
        ----------
        request : Request
            The HTTP request
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The HTTP response with the created instance data
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        entity_slug = self.kwargs.get('entity_slug')
        validated_data = serializer.validated_data.copy()
        # Extract child data
        chi_tiet_data = validated_data.pop('chi_tiet', None)
        thong_tin_thue_data = validated_data.pop('thong_tin_thue', None)
        # Create the instance with details
        instance = self.service.create_with_details(
            entity_slug=entity_slug,
            data=validated_data,
            chi_tiet_data=chi_tiet_data,
            thue_data=thong_tin_thue_data,
        )

        # Return the created instance with details
        instance = self.service.get_with_details(
            entity_slug=entity_slug, uuid=instance.uuid
        )
        serializer = self.get_serializer(instance)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update a PhieuKeToaNghiepVu instance.

        Parameters
        ----------
        request : Request
            The HTTP request
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The HTTP response with the updated instance data
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = self.kwargs.get('uuid')
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not instance:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data.copy()
        # Extract child data
        chi_tiet_data = validated_data.pop('chi_tiet', None)
        thong_tin_thue_data = validated_data.pop('thong_tin_thue', None)
        # Update the instance with details
        instance = self.service.update_with_details(
            uuid=uuid,
            data=validated_data,
            chi_tiet_data=chi_tiet_data,
            thue_data=thong_tin_thue_data,
        )

        # Return the updated instance with details
        instance = self.service.get_with_details(
            entity_slug=entity_slug, uuid=instance.uuid
        )
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a PhieuKeToaNghiepVu instance.

        Parameters
        ----------
        request : Request
            The HTTP request
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The HTTP response with no content
        """
        uuid = self.kwargs.get('uuid')
        success = self.service.delete(uuid=uuid)
        if not success:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        return Response(status=status.HTTP_204_NO_CONTENT)
