"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhieuThanhToanTamUng (Advance Payment Settlement Voucher Detail) Model
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietPhieuThanhToanTamUngModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the ChiTietPhieuThanhToanTamUngModel.
    """

    pass


class ChiTietPhieuThanhToanTamUngModelManager(Manager):
    """
    A custom defined ChiTietPhieuThanhToanTamUngModelManager that will act as an interface to handling the DB queries to the  # noqa: E501
    ChiTietPhieuThanhToanTamUngModel.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ChiTietPhieuThanhToanTamUngModelQueryset.
        """
        return ChiTietPhieuThanhToanTamUngModelQueryset(
            self.model, using=self._db
        )


class ChiTietPhieuThanhToanTamUngModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietPhieuThanhToanTamUngModel database will inherit from.  # noqa: E501
    The ChiTietPhieuThanhToanTamUngModel inherits functionality from the following MixIns:  # noqa: E501

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501

    phieu_thanh_toan: PhieuThanhToanTamUngModel
        Reference to the payment voucher.

    line: int
        Line number.

    tk_no: AccountModel
        Debit account.

    ma_kh: CustomerModel
        Customer code.

    tien_nt: Decimal
        Amount in foreign currency.

    tien: Decimal
        Amount.

    dien_giai: str
        Description.

    ma_loai_hd: str
        Invoice type code.

    ma_thue: str
        Tax code.

    ten_thue: str
        Tax name.

    thue_suat: Decimal
        Tax rate.

    tk_thue: AccountModel
        Tax account.

    ten_tk_thue: str
        Tax account name.

    so_ct0: str
        Original document number.

    so_ct2: str
        Secondary document number.

    ngay_ct0: datetime
        Original document date.

    ma_mau_ct: str
        Document template code.

    ma_mau_bc: str
        Report template code.

    ma_tc_thue: str
        Tax property code.

    ma_kh_thue: str
        Tax customer code.

    ten_kh_thue: str
        Tax customer name.

    dia_chi: str
        Address.

    ma_so_thue: str
        Tax code.

    ten_vt_thue: str
        Tax material name.

    thue_nt: Decimal
        Tax in foreign currency.

    thue: Decimal
        Tax amount.

    ma_kh9: CustomerModel
        Customer code 9.

    ten_kh9: str
        Customer name 9.

    ma_bp: BoPhanModel
        Department code.

    ma_vv: VuViecModel
        Case code.

    ma_hd: ContractModel
        Contract code.

    ma_dtt: DotThanhToanModel
        Payment installment code.

    ma_ku: KheUocModel
        Loan code.

    ma_phi: PhiModel
        Fee code.

    ma_sp: VatTuModel
        Product code.

    ma_lsx: str
        Production order code.

    ma_cp0: ChiPhiKhongHopLeModel
        Invalid expense code.

    id_tt: int
        Payment ID.
    """

    uuid = models.UUIDField(default=uuid4, editable=False, primary_key=True)
    phieu_thanh_toan = models.ForeignKey(
        'django_ledger.PhieuThanhToanTamUngModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_phieu_thanh_toan',
        verbose_name=_('Phiếu thanh toán'),
    )
    line = models.IntegerField(verbose_name=_("Số thứ tự"))
    tk_no = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_("Tài khoản nợ"),
    )
    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        verbose_name=_("Mã khách hàng"),
    )
    tien_nt = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_("Tiền ngoại tệ")
    )
    tien = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_("Tiền")
    )
    dien_giai = models.CharField(max_length=500, verbose_name=_("Diễn giải"))
    ma_loai_hd = models.CharField(
        max_length=50, verbose_name=_("Mã loại hóa đơn")
    )
    ma_thue = models.CharField(max_length=50, verbose_name=_("Mã thuế"))
    ten_thue = models.CharField(max_length=255, verbose_name=_("Tên thuế"))
    thue_suat = models.DecimalField(
        max_digits=5, decimal_places=2, verbose_name=_("Thuế suất")
    )
    tk_thue = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_phieu_thanh_toan_tk_thue',
        verbose_name=_("Tài khoản thuế"),
    )
    ten_tk_thue = models.CharField(
        max_length=255, verbose_name=_("Tên tài khoản thuế")
    )
    so_ct0 = models.CharField(max_length=50, verbose_name=_("Số chứng từ gốc"))
    so_ct2 = models.CharField(max_length=50, verbose_name=_("Số chứng từ 2"))
    ngay_ct0 = models.DateTimeField(verbose_name=_("Ngày chứng từ gốc"))
    ma_mau_ct = models.CharField(
        max_length=50, blank=True, verbose_name=_("Mã mẫu chứng từ")
    )
    ma_mau_bc = models.CharField(
        max_length=50, verbose_name=_("Mã mẫu báo cáo")
    )
    ma_tc_thue = models.CharField(
        max_length=50, verbose_name=_("Mã tính chất thuế")
    )
    ma_kh_thue = models.CharField(
        max_length=50, verbose_name=_("Mã khách hàng thuế")
    )
    ten_kh_thue = models.CharField(
        max_length=255, verbose_name=_("Tên khách hàng thuế")
    )
    dia_chi = models.CharField(max_length=500, verbose_name=_("Địa chỉ"))
    ma_so_thue = models.CharField(max_length=50, verbose_name=_("Mã số thuế"))
    ten_vt_thue = models.CharField(
        max_length=255, blank=True, verbose_name=_("Tên vật tư thuế")
    )
    thue_nt = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_("Thuế ngoại tệ")
    )
    thue = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_("Thuế")
    )
    ma_kh9 = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_phieu_thanh_toan_ma_kh9',
        verbose_name=_("Mã khách hàng 9"),
    )
    ten_kh9 = models.CharField(
        max_length=255, verbose_name=_("Tên khách hàng 9")
    )
    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.CASCADE,
        verbose_name=_("Mã bộ phận"),
    )
    ma_vv = models.ForeignKey(
        'django_ledger.VuViecModel',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        verbose_name=_("Mã vụ việc"),
    )
    ma_hd = models.ForeignKey(
        'django_ledger.ContractModel',
        on_delete=models.CASCADE,
        verbose_name=_("Mã hợp đồng"),
    )
    ma_dtt = models.ForeignKey(
        'django_ledger.DotThanhToanModel',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        verbose_name=_("Mã đợt thanh toán"),
    )
    ma_ku = models.ForeignKey(
        'django_ledger.KheUocModel',
        blank=True,
        null=True,
        on_delete=models.CASCADE,
        verbose_name=_("Mã khế ước"),
    )
    ma_phi = models.ForeignKey(
        'django_ledger.PhiModel',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        verbose_name=_("Mã phí"),
    )
    ma_sp = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        verbose_name=_("Mã sản phẩm"),
    )
    ma_lsx = models.CharField(
        max_length=50, blank=True, null=True, verbose_name=_("Mã lệnh sản xuất")
    )
    ma_cp0 = models.ForeignKey(
        'django_ledger.ChiPhiKhongHopLeModel',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        verbose_name=_("Mã chi phí"),
    )
    id_tt = models.IntegerField(verbose_name=_("ID thanh toán"))
    objects = ChiTietPhieuThanhToanTamUngModelManager()

    class Meta:
        abstract = True
        verbose_name = _('Chi tiết phiếu thanh toán tạm ứng')
        verbose_name_plural = _('Chi tiết phiếu thanh toán tạm ứng')
        indexes = [
            models.Index(fields=['phieu_thanh_toan']),
            models.Index(fields=['line']),
        ]

    def __str__(self):  # noqa: C901
        return f'{self.phieu_thanh_toan.so_ct} - {self.line}: {self.dien_giai}'


class ChiTietPhieuThanhToanTamUngModel(
    ChiTietPhieuThanhToanTamUngModelAbstract
):
    """
    Base Advance Payment Settlement Voucher Detail Model Implementation
    """

    class Meta(ChiTietPhieuThanhToanTamUngModelAbstract.Meta):
        abstract = False
        db_table = 'chi_tiet_phieu_thanh_toan_tam_ung'
