"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Views for PhieuNhapDieuChinhGiaHangMua model.
"""

from drf_spectacular.utils import (  # noqa: F401
    extend_schema,
    extend_schema_view,
)
from rest_framework import permissions, status, viewsets  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.decorators import api_exception_handler  # noqa: F401
from django_ledger.api.serializers.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_dieu_chinh_gia_hang_mua import (  # noqa: F401,
    PhieuNhapDieuChinhGiaHangMuaSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models import (  # noqa: F401,
    PhieuNhapDieuChinhGiaHangMuaModel,
)
from django_ledger.services.mua_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_dieu_chinh_gia_hang_mua import (  # noqa: F401,
    PhieuNhapDieuChinhGiaHangMuaService,
)


@extend_schema_view(
    list=extend_schema(tags=["Purchase Price Adjustment Receipt"]),
    create=extend_schema(tags=["Purchase Price Adjustment Receipt"]),
    retrieve=extend_schema(tags=["Purchase Price Adjustment Receipt"]),
    update=extend_schema(tags=["Purchase Price Adjustment Receipt"]),
    partial_update=extend_schema(tags=["Purchase Price Adjustment Receipt"]),
    destroy=extend_schema(tags=["Purchase Price Adjustment Receipt"]),
)
class PhieuNhapDieuChinhGiaHangMuaViewSet(EntityRelatedViewSet):
    """
    A ViewSet for PhieuNhapDieuChinhGiaHangMua model.
    """

    serializer_class = PhieuNhapDieuChinhGiaHangMuaSerializer  # noqa: F811
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'uuid'
    http_method_names = ['get', 'post', 'put', 'delete']

    def __init__(self, *args, **kwargs):  # noqa: C901
        """
        Initialize the ViewSet.

        Parameters
        ----------
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.service = PhieuNhapDieuChinhGiaHangMuaService()

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List PhieuNhapDieuChinhGiaHangMua instances.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug from kwargs
        entity_slug = self.kwargs['entity_slug']
        # Set user_model for service
        self.service.user_model = self.request.user
        # Get query parameters for filtering
        search_query = request.query_params.get('search', None)
        status_filter = request.query_params.get('status', None)
        from_date = request.query_params.get('from_date', None)
        to_date = request.query_params.get('to_date', None)
        # Get data from service, passing entity_slug directly
        instances = self.service.list(
            entity_slug=entity_slug,
            search_query=search_query,
            status=status_filter,
            from_date=from_date,
            to_date=to_date,
        )

        # Always paginate the queryset
        page = self.paginate_queryset(instances)
        serializer = self.get_serializer(page, many=True)
        return self.get_paginated_response(serializer.data)

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new PhieuNhapDieuChinhGiaHangMua instance with optional chi_tiet and thue.  # noqa: E501

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Validate data with context
        serializer = self.get_serializer(
            data=request.data,
            context={'entity_slug': entity_slug, 'request': request},
        )
        serializer.is_valid(raise_exception=True)
        # Extract validated data
        validated_data = serializer.validated_data
        # Extract chi_tiet and thue data if present
        chi_tiet_data = validated_data.pop('chi_tiet', [])
        thue_data = validated_data.pop('thue', [])
        # Use service to create the instance with details in a single transaction
        instance = self.service.create_with_details(
            entity_slug=entity_slug,
            data=validated_data,
            chi_tiet_data=chi_tiet_data,
            thue_data=thue_data,
        )

        # Serialize response
        response_serializer = self.get_serializer(instance)
        # Return response
        return Response(
            response_serializer.data, status=status.HTTP_201_CREATED
        )

    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a PhieuNhapDieuChinhGiaHangMua instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Get instance
        instance = self.service.get(
            entity_slug=entity_slug, uuid=kwargs['uuid']
        )
        if not instance:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Serialize response
        serializer = self.get_serializer(instance)
        # Return response
        return Response(serializer.data)

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update a PhieuNhapDieuChinhGiaHangMua instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Check if instance exists
        instance = self.service.get(
            entity_slug=entity_slug, uuid=kwargs['uuid']
        )
        if not instance:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Validate data
        serializer = self.get_serializer(
            instance,
            data=request.data,
            partial=kwargs.get('partial', False),
        )
        serializer.is_valid(raise_exception=True)
        # Extract validated data
        validated_data = serializer.validated_data
        # Extract chi_tiet and thue data if present
        chi_tiet_data = validated_data.pop('chi_tiet', None)
        thue_data = validated_data.pop('thue', None)
        # Use service to update the instance with details in a single transaction
        updated_instance = self.service.update_with_details(
            entity_slug=entity_slug,
            uuid=kwargs['uuid'],
            data=validated_data,
            chi_tiet_data=chi_tiet_data,
            thue_data=thue_data,
        )

        # Serialize response
        response_serializer = self.get_serializer(updated_instance)
        # Return response
        return Response(response_serializer.data)

    def partial_update(self, request, *args, **kwargs):  # noqa: C901
        """
        Partially update a PhieuNhapDieuChinhGiaHangMua instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a PhieuNhapDieuChinhGiaHangMua instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        try:
            # Delete instance
            self.service.delete(entity_slug=entity_slug, uuid=kwargs['uuid'])
        except PhieuNhapDieuChinhGiaHangMuaModel.DoesNotExist:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Return response
        return Response(status=status.HTTP_204_NO_CONTENT)
