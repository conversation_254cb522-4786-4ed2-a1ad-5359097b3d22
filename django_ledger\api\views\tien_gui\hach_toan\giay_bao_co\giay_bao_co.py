"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Views for GiayBaoCo model.
"""

from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import permissions, status
from rest_framework.response import Response

from django_ledger.api.decorators import api_exception_handler  # noqa: F401
from django_ledger.api.serializers.tien_gui.hach_toan.giay_bao_co.giay_bao_co import (  # noqa: E402
    GiayBaoCoSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: E402
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: E402
from django_ledger.models import GiayBaoCoModel  # noqa: E402
from django_ledger.services.tien_gui.hach_toan.giay_bao_co.giay_bao_co import (  # noqa: E402
    GiayBaoCoService,
)


@extend_schema_view(
    list=extend_schema(tags=["GiayBaoCo"]),
    create=extend_schema(tags=["GiayBaoCo"]),
    retrieve=extend_schema(tags=["GiayBaoCo"]),
    update=extend_schema(tags=["GiayBaoCo"]),
    partial_update=extend_schema(tags=["GiayBaoCo"]),
    destroy=extend_schema(tags=["GiayBaoCo"]),
)
class GiayBaoCoViewSet(EntityRelatedViewSet):
    """
    A ViewSet for GiayBaoCo model.
    """

    queryset = GiayBaoCoModel.objects.all()
    serializer_class = GiayBaoCoSerializer  # noqa: F811
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'uuid'
    http_method_names = ['get', 'post', 'put', 'delete']

    def __init__(self, *args, **kwargs):  # noqa: C901
        """
        Initialize the ViewSet with the service.

        Parameters
        ----------
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.service = GiayBaoCoService()

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List GiayBaoCo instances.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug from kwargs
        entity_slug = self.kwargs['entity_slug']
        # Set user_model for service
        self.service.user_model = self.request.user
        # Get data from service, passing entity_slug directly
        instances = self.service.list(entity_slug=entity_slug)
        # Always paginate the queryset
        page = self.paginate_queryset(instances)
        serializer = self.get_serializer(page, many=True)
        return self.get_paginated_response(serializer.data)

    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a GiayBaoCo instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Get instance
        instance = self.service.get(
            entity_slug=entity_slug, uuid=kwargs['uuid']
        )
        if not instance:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Serialize response
        serializer = self.get_serializer(instance)
        # Return response
        return Response(serializer.data)

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new GiayBaoCo instance with optional chi_tiet and phieu_ngan_hang.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Validate data with context
        serializer = self.get_serializer(
            data=request.data,
            context={'entity_slug': entity_slug, 'request': request},
        )
        serializer.is_valid(raise_exception=True)
        # Extract validated data
        validated_data = serializer.validated_data
        # Extract chi_tiet data if present
        chi_tiet_data = validated_data.pop('chi_tiet_giay_bao_co_items', [])
        phieu_ngan_hang_data = validated_data.pop(
            'phieu_ngan_hang_giay_bao_co_items', []
        )

        # Use service to create the instance with details in a single transaction
        instance, _, _ = self.service.create_with_details(
            entity_slug=entity_slug,
            data=validated_data,
            chi_tiet_data=chi_tiet_data,
            phieu_ngan_hang_data=phieu_ngan_hang_data,
        )

        # Serialize response
        response_serializer = self.get_serializer(instance)
        # Return response
        return Response(
            response_serializer.data, status=status.HTTP_201_CREATED
        )

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update a GiayBaoCo instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Check if instance exists
        instance = self.service.get(
            entity_slug=entity_slug, uuid=kwargs['uuid']
        )
        if not instance:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Validate data
        serializer = self.get_serializer(
            instance,
            data=request.data,
            partial=kwargs.get('partial', False),
        )
        serializer.is_valid(raise_exception=True)
        # Extract validated data
        validated_data = serializer.validated_data
        # Extract chi_tiet data if present
        chi_tiet_data = validated_data.pop('chi_tiet_giay_bao_co_items', [])
        phieu_ngan_hang_data = validated_data.pop(
            'phieu_ngan_hang_giay_bao_co_items', []
        )

        # Use service to update the instance with details in a single transaction
        updated_instance, _, _ = self.service.update_with_details(
            entity_slug=entity_slug,
            uuid=kwargs['uuid'],
            data=validated_data,
            chi_tiet_data=chi_tiet_data,
            phieu_ngan_hang_data=phieu_ngan_hang_data,
        )

        # Serialize response
        response_serializer = self.get_serializer(updated_instance)
        # Return response
        return Response(response_serializer.data)

    def partial_update(self, request, *args, **kwargs):  # noqa: C901
        """
        Partially update a GiayBaoCo instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a GiayBaoCo instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        try:
            # Delete instance
            self.service.delete(entity_slug=entity_slug, uuid=kwargs['uuid'])
        except GiayBaoCoModel.DoesNotExist:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Return response
        return Response(status=status.HTTP_204_NO_CONTENT)
