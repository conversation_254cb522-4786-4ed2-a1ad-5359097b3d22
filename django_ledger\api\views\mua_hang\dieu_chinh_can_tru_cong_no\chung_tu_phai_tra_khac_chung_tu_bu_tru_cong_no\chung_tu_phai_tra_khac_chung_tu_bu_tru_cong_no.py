"""
Views for ChungTuPhaiTraKhacChungTuBuTruCongNoModel.
"""

from drf_spectacular.utils import (  # noqa: F401
    extend_schema,
    extend_schema_view,
)
from rest_framework import permissions, status, viewsets  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.decorators import api_exception_handler  # noqa: F401
from django_ledger.api.serializers.mua_hang.dieu_chinh_can_tru_cong_no import (  # noqa: F401
    ChungTuPhaiTraKhacChungTuBuTruCongNoSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models.mua_hang.dieu_chinh_can_tru_cong_no import (  # noqa: F401,
    ChungTuPhaiTraKhacChungTuBuTruCongNoModel,
)
from django_ledger.services.mua_hang.dieu_chinh_can_tru_cong_no import (  # noqa: F401,
    ChungTuPhaiTraKhacChungTuBuTruCongNoService,
)


@extend_schema_view(
    list=extend_schema(
        summary="List debt settlement vouchers",
        description="Retrieve a list of debt settlement vouchers for the entity.",
        tags=["Debt Settlement Vouchers"],
    ),
    create=extend_schema(
        summary="Create debt settlement voucher",
        description="Create a new debt settlement voucher.",
        tags=["Debt Settlement Vouchers"],
    ),
    retrieve=extend_schema(
        summary="Retrieve debt settlement voucher",
        description="Retrieve a specific debt settlement voucher by UUID.",
        tags=["Debt Settlement Vouchers"],
    ),
    update=extend_schema(
        summary="Update debt settlement voucher",
        description="Update a specific debt settlement voucher.",
        tags=["Debt Settlement Vouchers"],
    ),
    partial_update=extend_schema(
        summary="Partially update debt settlement voucher",
        description="Partially update a specific debt settlement voucher.",
        tags=["Debt Settlement Vouchers"],
    ),
    destroy=extend_schema(
        summary="Delete debt settlement voucher",
        description="Delete a specific debt settlement voucher.",
        tags=["Debt Settlement Vouchers"],
    ),
)
class ChungTuPhaiTraKhacChungTuBuTruCongNoViewSet(EntityRelatedViewSet):
    """
    A ViewSet for ChungTuPhaiTraKhacChungTuBuTruCongNoModel.
    """

    serializer_class = (
        ChungTuPhaiTraKhacChungTuBuTruCongNoSerializer  # noqa: F811
    )
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'uuid'
    http_method_names = ['get', 'post', 'put', 'delete']

    def __init__(self, *args, **kwargs):  # noqa: C901
        """
        Initialize the ViewSet.

        Parameters
        ----------
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.service = ChungTuPhaiTraKhacChungTuBuTruCongNoService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for this ViewSet.

        Returns
        -------
        QuerySet
            The filtered queryset
        """
        entity_slug = self.kwargs.get('entity_slug')
        if not entity_slug:
            return ChungTuPhaiTraKhacChungTuBuTruCongNoModel.objects.none()

        queryset = self.service.get_by_entity(entity_slug)
        # Filter by status if provided
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        # Filter by date range if provided
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(ngay_ct__range=[start_date, end_date])
        # Filter by document number if provided
        so_ct = self.request.query_params.get('so_ct')
        if so_ct:
            queryset = queryset.filter(so_ct__icontains=so_ct)
        return queryset.order_by('-ngay_ct', '-created')

    @api_exception_handler
    def perform_create(self, serializer):  # noqa: C901
        """
        Perform the creation of a new instance.

        Parameters
        ----------
        serializer : ChungTuPhaiTraKhacChungTuBuTruCongNoSerializer
            The serializer instance
        """
        entity_model = self.get_entity_model()
        instance = self.service.create(entity_model, serializer.validated_data)
        serializer.instance = instance

    @api_exception_handler
    def perform_update(self, serializer):  # noqa: C901
        """
        Perform the update of an existing instance.

        Parameters
        ----------
        serializer : ChungTuPhaiTraKhacChungTuBuTruCongNoSerializer
            The serializer instance
        """
        instance = self.service.update(
            str(serializer.instance.uuid), serializer.validated_data
        )
        serializer.instance = instance

    @api_exception_handler
    def perform_destroy(self, instance):  # noqa: C901
        """
        Perform the deletion of an instance.

        Parameters
        ----------
        instance : ChungTuPhaiTraKhacChungTuBuTruCongNoModel
            The instance to delete
        """
        success = self.service.delete(str(instance.uuid))
        if not success:
            return Response(
                {'error': 'Failed to delete the debt settlement voucher'},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @extend_schema(
        summary="Get active debt settlement vouchers",
        description="Retrieve only active debt settlement vouchers.",
        tags=["Debt Settlement Vouchers"],
    )
    @api_exception_handler
    def get_active(self, request, entity_slug=None):  # noqa: F811,
        """
        Get active debt settlement vouchers.

        Parameters
        ----------
        request : Request
            The request object
        entity_slug : str
            The entity slug

        Returns
        -------
        Response
            The response containing active vouchers
        """
        queryset = self.service.get_active(entity_slug)
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @extend_schema(
        summary="Calculate totals for debt settlement voucher",
        description="Calculate total amounts from detail lines.",
        tags=["Debt Settlement Vouchers"],
    )
    @api_exception_handler
    def calculate_totals(
        self, request, entity_slug=None, uuid=None
    ):  # noqa: F811,
        """
        Calculate totals for a debt settlement voucher.

        Parameters
        ----------
        request : Request
            The request object
        entity_slug : str
            The entity slug
        uuid : str
            The voucher UUID

        Returns
        -------
        Response
            The response containing calculated totals
        """
        try:
            totals = self.service.calculate_totals(uuid)
            return Response(totals, status=status.HTTP_200_OK)
        except Exception as e:

            return Response(
                {'message': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )

    @extend_schema(
        summary="Update totals for debt settlement voucher",
        description="Update total amounts based on detail lines.",
        tags=["Debt Settlement Vouchers"],
    )
    @api_exception_handler
    def update_totals(
        self, request, entity_slug=None, uuid=None
    ):  # noqa: F811,
        """
        Update totals for a debt settlement voucher.

        Parameters
        ----------
        request : Request
            The request object
        entity_slug : str
            The entity slug
        uuid : str
            The voucher UUID

        Returns
        -------
        Response
            The response containing updated voucher
        """
        try:
            instance = self.service.update_totals(uuid)
            serializer = self.get_serializer(instance)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:

            return Response(
                {'message': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )
