"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Views for PhieuThu model.
"""

from drf_spectacular.utils import (  # noqa: F401
    extend_schema,
    extend_schema_view,
)
from rest_framework import permissions, status, viewsets  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.decorators import api_exception_handler  # noqa: F401
from django_ledger.api.serializers.tien_mat.hach_toan.phieu_thu.phieu_thu import (  # noqa: F401,
    PhieuThuSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models import PhieuThuModel  # noqa: F401,
from django_ledger.services.tien_mat.hach_toan.phieu_thu.phieu_thu import (  # noqa: F401,
    PhieuThuService,
)


@extend_schema_view(
    list=extend_schema(tags=["PhieuThu"]),
    create=extend_schema(tags=["PhieuThu"]),
    retrieve=extend_schema(tags=["PhieuThu"]),
    update=extend_schema(tags=["PhieuThu"]),
    partial_update=extend_schema(tags=["PhieuThu"]),
    destroy=extend_schema(tags=["PhieuThu"]),
)
class PhieuThuViewSet(EntityRelatedViewSet):
    """
    A ViewSet for PhieuThu model.
    """

    serializer_class = PhieuThuSerializer  # noqa: F811
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'uuid'
    http_method_names = ['get', 'post', 'put', 'patch', 'delete']

    def __init__(self, *args, **kwargs):  # noqa: C901
        """
        Initialize the ViewSet.

        Parameters
        ----------
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.service = PhieuThuService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for the ViewSet.

        Returns
        -------
        QuerySet
            The queryset for the ViewSet
        """
        entity_slug = self.kwargs.get('entity_slug')
        search_query = self.request.query_params.get('search')
        status_filter = self.request.query_params.get('status')
        from_date = self.request.query_params.get('from_date')
        to_date = self.request.query_params.get('to_date')
        return self.service.list(
            entity_slug=entity_slug,
            search_query=search_query,
            status=status_filter,
            from_date=from_date,
            to_date=to_date,
        )

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new PhieuThu instance.

        Parameters
        ----------
        request : Request
            The request object
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The response object
        """
        entity_slug = kwargs.get('entity_slug')
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        instance = self.service.create(
            entity_slug=entity_slug, data=serializer.validated_data
        )

        return Response(
            self.get_serializer(instance).data,
            status=status.HTTP_201_CREATED,
        )

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update an existing PhieuThu instance.

        Parameters
        ----------
        request : Request
            The request object
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The response object
        """
        entity_slug = kwargs.get('entity_slug')
        uuid = kwargs.get('uuid')
        partial = kwargs.pop('partial', False)
        serializer = self.get_serializer(data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        instance = self.service.update(
            entity_slug=entity_slug,
            uuid=uuid,
            data=serializer.validated_data,
        )

        return Response(
            self.get_serializer(instance).data, status=status.HTTP_200_OK
        )

    def partial_update(self, request, *args, **kwargs):  # noqa: C901
        """
        Partially update an existing PhieuThu instance.

        Parameters
        ----------
        request : Request
            The request object
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The response object
        """
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a PhieuThu instance.

        Parameters
        ----------
        request : Request
            The request object
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments

        Returns
        -------
        Response
            The response object
        """
        entity_slug = kwargs.get('entity_slug')
        uuid = kwargs.get('uuid')
        self.service.delete(entity_slug=entity_slug, uuid=uuid)
        return Response(status=status.HTTP_204_NO_CONTENT)
