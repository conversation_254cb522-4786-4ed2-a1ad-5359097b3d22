"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

CapNhatKiemKe (Inventory Update) view implementation.
"""

from rest_framework import permissions, status
from rest_framework.response import Response

from django_ledger.api.decorators import api_exception_handler  # noqa: F401
from django_ledger.api.serializers.ton_kho.kiem_ke.cap_nhat_kiem_ke import (
    CapNhatKiemKeModelSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: E402
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: E402
from django_ledger.services.ton_kho.kiem_ke.cap_nhat_kiem_ke import (  # noqa: E402
    CapNhatKiemKeService,
)


class CapNhatKiemKeViewSet(EntityRelatedViewSet):
    """
    ViewSet for CapNhatKiemKeModel.
    """

    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'uuid'
    http_method_names = ['get', 'post', 'put', 'delete']

    def __init__(self, *args, **kwargs):  # noqa: C901
        super().__init__(*args, **kwargs)
        self.service = CapNhatKiemKeService()

    def get_serializer_context(self):  # noqa: C901
        """
        Returns the serializer context.
        """
        return {
            'request': self.request,
            'entity_slug': self.kwargs['entity_slug'],
        }

    def get_serializer(self, *args, **kwargs):  # noqa: C901
        """
        Returns the serializer instance.
        """
        kwargs['context'] = self.get_serializer_context()
        return CapNhatKiemKeModelSerializer(*args, **kwargs)

    def list(self, request, entity_slug=None):  # noqa: F811,
        """
        Lists CapNhatKiemKeModel instances.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        """
        try:
            queryset = self.service.list(entity_slug=entity_slug)
            # Apply pagination
            page = self.paginate_queryset(queryset)
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        except Exception as e:

            return Response(
                {'message': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )

    def retrieve(self, request, entity_slug=None, uuid=None):  # noqa: F811,
        """
        Retrieves a CapNhatKiemKeModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        uuid : str
            The UUID of the CapNhatKiemKeModel to retrieve.
        """
        try:
            instance = self.service.get_by_id(
                entity_slug=entity_slug, uuid=uuid
            )
            if not instance:
                return Response(
                    {'detail': 'CapNhatKiemKe not found.'},
                    status=status.HTTP_404_NOT_FOUND,
                )

            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        except Exception as e:

            return Response(
                {'message': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )

    @api_exception_handler
    def create(self, request, entity_slug=None):  # noqa: F811,
        """
        Creates a new CapNhatKiemKeModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        """
        try:
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                # Extract data from serializer
                validated_data = serializer.validated_data
                # Create the CapNhatKiemKeModel instance
                instance = self.service.create(
                    entity_slug=entity_slug, data=validated_data
                )

                if not instance:
                    return Response(
                        {'detail': 'Entity not found.'},
                        status=status.HTTP_404_NOT_FOUND,
                    )

                # Return the created instance
                serializer = self.get_serializer(instance)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            return Response(
                serializer.errors, status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:

            return Response(
                {'message': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )

    @api_exception_handler
    def update(self, request, entity_slug=None, uuid=None):  # noqa: F811,
        """
        Updates a CapNhatKiemKeModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        uuid : str
            The UUID of the CapNhatKiemKeModel to update.
        """
        try:
            instance = self.service.get_by_id(
                entity_slug=entity_slug, uuid=uuid
            )
            if not instance:
                return Response(
                    {'detail': 'CapNhatKiemKe not found.'},
                    status=status.HTTP_404_NOT_FOUND,
                )

            serializer = self.get_serializer(instance, data=request.data)
            if serializer.is_valid():
                # Extract data from serializer
                validated_data = serializer.validated_data
                # Update the CapNhatKiemKeModel instance
                instance = self.service.update(
                    entity_slug=entity_slug,
                    uuid=uuid,
                    data=validated_data,
                )

                # Refresh instance to get all related data
                instance = self.service.get_by_id(
                    entity_slug=entity_slug, uuid=uuid
                )
                serializer = self.get_serializer(instance)
                return Response(serializer.data)
            return Response(
                serializer.errors, status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:

            return Response(
                {'message': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )

    @api_exception_handler
    def destroy(self, request, entity_slug=None, uuid=None):  # noqa: F811,
        """
        Deletes a CapNhatKiemKeModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        uuid : str
            The UUID of the CapNhatKiemKeModel to delete.
        """
        try:
            success = self.service.delete(entity_slug=entity_slug, uuid=uuid)
            if not success:
                return Response(
                    {'detail': 'CapNhatKiemKe not found.'},
                    status=status.HTTP_404_NOT_FOUND,
                )

            return Response(status=status.HTTP_204_NO_CONTENT)
        except Exception as e:

            return Response(
                {'message': str(e)}, status=status.HTTP_400_BAD_REQUEST
            )
