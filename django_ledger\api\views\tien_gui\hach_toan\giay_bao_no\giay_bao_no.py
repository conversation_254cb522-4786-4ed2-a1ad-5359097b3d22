"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ViewSet for BankTransferDocument model.
"""

from drf_spectacular.utils import extend_schema, extend_schema_view
from rest_framework import permissions, status
from rest_framework.response import Response

from django_ledger.api.decorators import api_exception_handler  # noqa: F401
from django_ledger.api.serializers.tien_gui.hach_toan.giay_bao_no import (  # noqa: E402
    BankTransferDocumentSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: E402
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: E402
from django_ledger.services.tien_gui.hach_toan.giay_bao_no import (  # noqa: E402
    BankTransferDocumentService,
)


@extend_schema_view(
    list=extend_schema(tags=["Bank Transfer Documents"]),
    create=extend_schema(tags=["Bank Transfer Documents"]),
    retrieve=extend_schema(tags=["Bank Transfer Documents"]),
    update=extend_schema(tags=["Bank Transfer Documents"]),
    partial_update=extend_schema(tags=["Bank Transfer Documents"]),
    destroy=extend_schema(tags=["Bank Transfer Documents"]),
)
class BankTransferDocumentViewSet(EntityRelatedViewSet):
    """
    A ViewSet for BankTransferDocument model.
    """

    serializer_class = BankTransferDocumentSerializer  # noqa: F811
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811
    lookup_field = 'uuid'
    http_method_names = ['get', 'post', 'put', 'delete']

    def __init__(self, *args, **kwargs):  # noqa: C901
        """
        Initialize the ViewSet.

        Parameters
        ----------
        *args : tuple
            Variable length argument list
        **kwargs : dict
            Arbitrary keyword arguments
        """
        super().__init__(*args, **kwargs)
        self.service = BankTransferDocumentService()

    def get_queryset(self):  # noqa: C901
        """
        Get the queryset for this ViewSet.

        Returns
        -------
        QuerySet
            The queryset
        """
        entity_slug = self.kwargs['entity_slug']
        self.service.user_model = self.request.user
        return self.service.list(entity_slug=entity_slug)

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        List BankTransferDocument instances.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug from kwargs
        entity_slug = self.kwargs['entity_slug']
        # Set user_model for service
        self.service.user_model = self.request.user
        # Get data from service, passing entity_slug directly
        instances = self.service.list(entity_slug=entity_slug)
        # Always paginate the queryset
        page = self.paginate_queryset(instances)
        serializer = self.get_serializer(page, many=True)
        return self.get_paginated_response(serializer.data)

    @api_exception_handler
    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Create a new BankTransferDocument instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Validate data
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # Extract validated data
        validated_data = serializer.validated_data
        # Extract detail data if present
        chi_tiet_data = validated_data.pop('chi_tiet', [])
        thue_data = validated_data.pop('thue', [])
        phi_ngan_hang_data = validated_data.pop('phi_ngan_hang', [])
        # Use service to create the instance with details in a single transaction
        instance = self.service.create_with_details(
            entity_slug=entity_slug,
            data=validated_data,
            chi_tiet_data=chi_tiet_data,
            thue_data=thue_data,
            phi_ngan_hang_data=phi_ngan_hang_data,
        )

        # Serialize response
        response_serializer = self.get_serializer(instance)
        # Return response
        return Response(
            response_serializer.data, status=status.HTTP_201_CREATED
        )

    def retrieve(self, request, *args, **kwargs):  # noqa: C901
        """
        Retrieve a BankTransferDocument instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Get instance
        instance = self.service.get(
            entity_slug=entity_slug, uuid=kwargs['uuid']
        )
        if not instance:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Serialize response
        serializer = self.get_serializer(instance)
        # Return response
        return Response(serializer.data)

    @api_exception_handler
    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Update an existing BankTransferDocument instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Get instance
        instance = self.service.get(
            entity_slug=entity_slug, uuid=kwargs['uuid']
        )
        if not instance:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Validate data
        serializer = self.get_serializer(
            instance,
            data=request.data,
            partial=kwargs.get('partial', False),
        )
        serializer.is_valid(raise_exception=True)
        # Extract validated data
        validated_data = serializer.validated_data
        # Extract detail data if present
        chi_tiet_data = validated_data.pop('chi_tiet', [])
        thue_data = validated_data.pop('thue', [])
        phi_ngan_hang_data = validated_data.pop('phi_ngan_hang', [])
        # Use service to update the instance with details in a single transaction
        instance = self.service.update_with_details(
            entity_slug=entity_slug,
            uuid=kwargs['uuid'],
            data=validated_data,
            chi_tiet_data=chi_tiet_data,
            thue_data=thue_data,
            phi_ngan_hang_data=phi_ngan_hang_data,
        )

        # Serialize response
        response_serializer = self.get_serializer(instance)
        # Return response
        return Response(response_serializer.data)

    @api_exception_handler
    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Delete a BankTransferDocument instance.

        Parameters
        ----------
        request : Request
            The request object

        Returns
        -------
        Response
            The response object
        """
        # Get entity_slug
        entity_slug = self.kwargs['entity_slug']
        # Delete instance
        deleted = self.service.delete(
            entity_slug=entity_slug, uuid=kwargs['uuid']
        )
        if not deleted:
            return Response(
                {'detail': 'Not found.'}, status=status.HTTP_404_NOT_FOUND
            )
        # Return response
        return Response(status=status.HTTP_204_NO_CONTENT)
