"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.account import (  # noqa: F401
    AccountModelSerializer,
)
from django_ledger.api.serializers.finance import (  # noqa: F401
    PhuongThucThanhToanModelSerializer,
)
from django_ledger.api.serializers.group import (  # noqa: F401,
    GroupModelSerializer,
)
from django_ledger.api.serializers.han_thanh_toan import (  # noqa: F401,
    HanThanhToanModelSerializer,
)
from django_ledger.api.serializers.khu_vuc import (  # noqa: F401,
    KhuVucModelSerializer,
)
from django_ledger.api.serializers.nhan_vien import (  # noqa: F401,
    NhanVienModelSerializer,
)
from django_ledger.models import CustomerModel  # noqa: F401,
from django_ledger.models import EntityModel


class CustomerModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the CustomerModel

    This serializer includes additional fields with "_data" suffix for all reference fields,  # noqa: E501
    providing the complete nested object data for each reference.
    """

    # Reference data fields
    sales_rep_data = serializers.SerializerMethodField()
    account_data = serializers.SerializerMethodField()
    payment_term_data = serializers.SerializerMethodField()
    payment_method_data = serializers.SerializerMethodField()
    customer_group1_data = serializers.SerializerMethodField()
    customer_group2_data = serializers.SerializerMethodField()
    customer_group3_data = serializers.SerializerMethodField()
    region_data = serializers.SerializerMethodField()

    class Meta:
        model = CustomerModel
        fields = [
            # Basic Fields
            "uuid",
            "entity_model",
            "customer_number",
            "description",
            "active",
            "hidden",
            # Core Fields
            "customer_code",
            "is_customer",
            "is_vendor",
            "customer_type",
            "customer_name",
            "alternative_name",
            "address",
            "tax_code",
            "contact_person",
            "enterprise_name",
            "search_keywords",
            # Relations
            "sales_rep",
            "sales_rep_data",
            "account",
            "account_data",
            "payment_term",
            "payment_term_data",
            "payment_method",
            "payment_method_data",
            # Groups and Region
            "customer_group1",
            "customer_group1_data",
            "customer_group2",
            "customer_group2_data",
            "customer_group3",
            "customer_group3_data",
            "region",
            "region_data",
            # Contact Info
            "phone",
            "fax",
            "email",
            "website",
            # Additional Info
            "notes",
            "status",
            "credit_limit",
            # Personal/Organization Info
            "birth_date",
            "id_number",
            "legal_representative",
            "representative_position",
            # Bank Info
            "bank_account",
            "bank_name",
            "bank_branch",
            # Location and Business Info
            "province",
            "description_text",
            "delivery_address",
            "business_field",
            # E-invoice Info
            "use_einvoice",
            "einvoice_email",
            "representative",
            # Additional Data
            "additional_info",
            # Timestamps
            "created",
            "updated",
        ]
        read_only_fields = [
            "uuid",
            "created",
            "updated",
            "sales_rep_data",
            "account_data",
            "payment_term_data",
            "payment_method_data",
            "customer_group1_data",
            "customer_group2_data",
            "customer_group3_data",
            "region_data",
        ]

    def get_sales_rep_data(self, obj):  # noqa: C901
        """
        Returns the sales representative data for the sales_rep field.
        """
        if obj.sales_rep:
            return NhanVienModelSerializer(obj.sales_rep).data
        return None

    def get_account_data(self, obj):  # noqa: C901
        """
        Returns the account data for the account field.
        """
        if obj.account:
            return AccountModelSerializer(obj.account).data
        return None

    def get_payment_term_data(self, obj):  # noqa: C901
        """
        Returns the payment term data for the payment_term field.
        """
        if obj.payment_term:
            return HanThanhToanModelSerializer(obj.payment_term).data
        return None

    def get_payment_method_data(self, obj):  # noqa: C901
        """
        Returns the payment method data for the payment_method field.
        """
        if obj.payment_method:
            return PhuongThucThanhToanModelSerializer(obj.payment_method).data
        return None

    def get_customer_group1_data(self, obj):  # noqa: C901
        """
        Returns the customer group 1 data for the customer_group1 field.
        """
        if obj.customer_group1:
            return GroupModelSerializer(obj.customer_group1).data
        return None

    def get_customer_group2_data(self, obj):  # noqa: C901
        """
        Returns the customer group 2 data for the customer_group2 field.
        """
        if obj.customer_group2:
            return GroupModelSerializer(obj.customer_group2).data
        return None

    def get_customer_group3_data(self, obj):  # noqa: C901
        """
        Returns the customer group 3 data for the customer_group3 field.
        """
        if obj.customer_group3:
            return GroupModelSerializer(obj.customer_group3).data
        return None

    def get_region_data(self, obj):  # noqa: C901
        """
        Returns the region data for the region field.
        """
        if obj.region:
            return KhuVucModelSerializer(obj.region).data
        return None

    def validate(self, attrs):  # noqa: C901
        """
        Object-level validation to ensure customer_code uniqueness.
        """
        attrs = super().validate(attrs)

        # Check customer_code uniqueness
        customer_code = attrs.get('customer_code')

        if customer_code:
            # Get entity from context
            entity_model = None

            if hasattr(self, 'context') and 'request' in self.context:
                entity_slug = self.context['view'].kwargs.get('entity_slug')

                if entity_slug:
                    try:
                        entity_model = EntityModel.objects.get(slug=entity_slug)
                    except EntityModel.DoesNotExist:
                        pass

            if entity_model:
                # Check for existing customer with same code in this entity
                existing_customer = CustomerModel.objects.filter(
                    entity_model=entity_model,
                    customer_code=customer_code
                ).exclude(pk=self.instance.pk if self.instance else None).first()

                if existing_customer:
                    raise serializers.ValidationError({
                        'customer_code': f"Customer code '{customer_code}' already exists in this entity. "
                                       f"Please use a different customer code."
                    })

        return attrs
